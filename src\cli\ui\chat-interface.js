import inquirer from 'inquirer';
import chalk from 'chalk';
import boxen from 'boxen';
import ora from 'ora';
import wordWrap from 'word-wrap';
import configManager from '../../config/config-manager.js';
import ProviderFactory from '../../providers/index.js';
import logger from '../../utils/logger.js';

export class ChatInterface {
  constructor() {
    this.messages = [];
    this.provider = null;
    this.currentModel = null;
    this.isRunning = false;
  }

  /**
   * Start the chat interface
   */
  async start() {
    try {
      await this.initialize();
      this.showChatHeader();
      this.showInstructions();
      
      this.isRunning = true;
      
      while (this.isRunning) {
        await this.chatLoop();
      }
      
    } catch (error) {
      logger.error('Chat interface error:', error.message);
      console.log(chalk.red(`Error: ${error.message}`));
    }
  }

  /**
   * Initialize the chat interface
   */
  async initialize() {
    const activeProvider = configManager.getActiveProvider();
    
    if (!activeProvider) {
      throw new Error('No active provider configured');
    }

    this.provider = ProviderFactory.createProvider(activeProvider.name);
    this.currentModel = activeProvider.config.defaultModel;
    
    logger.info(`Chat initialized with ${activeProvider.name} using ${this.currentModel}`);
  }

  /**
   * Show chat header
   */
  showChatHeader() {
    console.clear();
    
    const activeProvider = configManager.getActiveProvider();
    const headerText = `Chat with ${activeProvider.name.charAt(0).toUpperCase() + activeProvider.name.slice(1)}`;
    const modelText = `Model: ${this.currentModel}`;
    
    const header = boxen(
      chalk.bold.cyan(headerText) + '\n' + chalk.gray(modelText),
      {
        padding: 1,
        borderStyle: 'round',
        borderColor: 'cyan',
        textAlignment: 'center'
      }
    );
    
    console.log(header);
  }

  /**
   * Show chat instructions
   */
  showInstructions() {
    const instructions = [
      chalk.gray('Commands:'),
      chalk.gray('  /help    - Show this help'),
      chalk.gray('  /model   - Change model'),
      chalk.gray('  /clear   - Clear conversation'),
      chalk.gray('  /config  - Open configuration'),
      chalk.gray('  /exit    - Exit chat'),
      chalk.gray('  /quit    - Exit chat'),
      chalk.gray(''),
      chalk.gray('Type your message and press Enter to send.')
    ].join('\n');
    
    console.log(instructions);
    console.log();
  }

  /**
   * Main chat loop
   */
  async chatLoop() {
    try {
      const { message } = await inquirer.prompt([
        {
          type: 'input',
          name: 'message',
          message: chalk.white('▶'),
          prefix: '',
          suffix: '',
          transformer: (input, _, flags) => {
            if (!input && !flags.isFinal) {
              return chalk.gray('Type your message or /path/to/file');
            }
            return input;
          },
          validate: (input) => {
            if (!input.trim()) {
              return 'Please enter a message';
            }
            return true;
          }
        }
      ]);

      const trimmedMessage = message.trim();
      
      // Handle commands
      if (trimmedMessage.startsWith('/')) {
        await this.handleCommand(trimmedMessage);
        return;
      }

      // Send message to LLM
      await this.sendMessage(trimmedMessage);
      
    } catch (error) {
      if (error.isTtyError) {
        // Handle TTY errors gracefully
        this.isRunning = false;
      } else {
        logger.error('Chat loop error:', error.message);
        console.log(chalk.red(`Error: ${error.message}`));
      }
    }
  }

  /**
   * Send message to LLM and display response
   */
  async sendMessage(userMessage) {
    // Add user message to conversation
    this.messages.push({ role: 'user', content: userMessage });
    
    const spinner = ora('Thinking...').start();
    
    try {
      const systemPrompt = configManager.get('chat.systemPrompt');
      
      const response = await this.provider.sendMessage(this.messages, {
        model: this.currentModel,
        systemPrompt: systemPrompt
      });
      
      spinner.stop();
      
      // Add assistant response to conversation
      this.messages.push({ role: 'assistant', content: response.content });
      
      // Display response
      this.displayResponse(response);
      
    } catch (error) {
      spinner.stop();
      console.log(chalk.red(`Error: ${error.message}`));
      logger.error('Send message error:', error.message);
    }
  }

  /**
   * Display LLM response
   */
  displayResponse(response) {
    const wrappedContent = wordWrap(response.content, { width: 80, indent: '' });
    
    console.log();
    console.log(chalk.green('Assistant:'));
    console.log(wrappedContent);
    
    // Show token usage if enabled
    if (configManager.get('chat.showTokenCount') && response.usage) {
      const tokenInfo = chalk.gray(
        `[Tokens: ${response.usage.totalTokens} (${response.usage.promptTokens} + ${response.usage.completionTokens})]`
      );
      console.log(tokenInfo);
    }
    
    console.log();
  }

  /**
   * Handle chat commands
   */
  async handleCommand(command) {
    const [cmd, ...args] = command.slice(1).split(' ');
    
    switch (cmd.toLowerCase()) {
      case 'help':
        this.showInstructions();
        break;
        
      case 'model':
        await this.changeModel();
        break;
        
      case 'clear':
        await this.clearConversation();
        break;
        
      case 'config':
        await this.openConfiguration();
        break;
        
      case 'exit':
      case 'quit':
        this.isRunning = false;
        console.log(chalk.yellow('Goodbye!'));
        break;
        
      case 'history':
        this.showHistory();
        break;
        
      case 'save':
        await this.saveConversation(args.join(' '));
        break;
        
      default:
        console.log(chalk.red(`Unknown command: /${cmd}`));
        console.log(chalk.gray('Type /help for available commands'));
    }
  }

  /**
   * Change the current model
   */
  async changeModel() {
    const { ModelSelector } = await import('./components/model-selector.js');
    const activeProvider = configManager.getActiveProvider();
    
    const newModel = await ModelSelector.quickSelectModel(activeProvider.name);
    
    if (newModel && newModel !== this.currentModel) {
      this.currentModel = newModel;
      console.log(chalk.green(`Model changed to ${newModel}`));
      this.showChatHeader();
    }
  }

  /**
   * Clear the conversation
   */
  async clearConversation() {
    const { confirm } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: 'Clear the conversation history?',
        default: false
      }
    ]);

    if (confirm) {
      this.messages = [];
      console.log(chalk.yellow('Conversation cleared'));
      this.showChatHeader();
      this.showInstructions();
    }
  }

  /**
   * Open configuration screen
   */
  async openConfiguration() {
    const { ConfigScreen } = await import('./config-screen.js');
    
    const result = await ConfigScreen.show();
    
    if (result === 'exit') {
      this.isRunning = false;
    } else {
      // Reinitialize in case provider changed
      try {
        await this.initialize();
        this.showChatHeader();
        console.log(chalk.green('Configuration updated'));
      } catch (error) {
        console.log(chalk.red(`Error reinitializing: ${error.message}`));
      }
    }
  }

  /**
   * Show conversation history
   */
  showHistory() {
    if (this.messages.length === 0) {
      console.log(chalk.yellow('No conversation history'));
      return;
    }

    console.log(chalk.bold('\nConversation History:\n'));
    
    this.messages.forEach((msg, index) => {
      const role = msg.role === 'user' ? chalk.blue('You') : chalk.green('Assistant');
      const content = wordWrap(msg.content, { width: 70, indent: '  ' });
      
      console.log(`${index + 1}. ${role}:`);
      console.log(content);
      console.log();
    });
  }

  /**
   * Save conversation to file
   */
  async saveConversation(filename) {
    if (this.messages.length === 0) {
      console.log(chalk.yellow('No conversation to save'));
      return;
    }

    try {
      const fs = await import('fs/promises');
      const path = await import('path');
      
      const defaultFilename = filename || `conversation-${Date.now()}.txt`;
      const filepath = path.resolve(defaultFilename);
      
      let content = `LLM CLI Conversation\n`;
      content += `Date: ${new Date().toISOString()}\n`;
      content += `Provider: ${this.provider.name}\n`;
      content += `Model: ${this.currentModel}\n`;
      content += `\n${'='.repeat(50)}\n\n`;
      
      this.messages.forEach((msg, index) => {
        const role = msg.role === 'user' ? 'You' : 'Assistant';
        content += `${index + 1}. ${role}:\n${msg.content}\n\n`;
      });
      
      await fs.writeFile(filepath, content, 'utf8');
      console.log(chalk.green(`Conversation saved to ${filepath}`));
      
    } catch (error) {
      console.log(chalk.red(`Error saving conversation: ${error.message}`));
    }
  }
}
