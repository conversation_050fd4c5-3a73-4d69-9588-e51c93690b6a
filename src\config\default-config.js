export const defaultConfig = {
  // Current active provider
  activeProvider: null,
  
  // Provider configurations
  providers: {
    openai: {
      name: 'OpenAI',
      apiKey: null,
      baseURL: 'https://api.openai.com/v1',
      models: [
        'gpt-4',
        'gpt-4-turbo',
        'gpt-3.5-turbo',
        'gpt-4o',
        'gpt-4o-mini'
      ],
      defaultModel: 'gpt-4',
      maxTokens: 4096,
      temperature: 0.7
    },
    
    anthropic: {
      name: 'Anthropic',
      apiKey: null,
      baseURL: 'https://api.anthropic.com/v1',
      models: [
        'claude-3-5-sonnet-20241022',
        'claude-3-5-haiku-20241022',
        'claude-3-opus-20240229',
        'claude-3-sonnet-20240229',
        'claude-3-haiku-20240307'
      ],
      defaultModel: 'claude-3-5-sonnet-20241022',
      maxTokens: 4096,
      temperature: 0.7
    },
    
    deepseek: {
      name: 'DeepSeek',
      apiKey: null,
      baseURL: 'https://api.deepseek.com/v1',
      models: [
        'deepseek-chat',
        'deepseek-coder',
        'deepseek-reasoner'
      ],
      defaultModel: 'deepseek-chat',
      maxTokens: 4096,
      temperature: 0.7
    }
  },
  
  // UI preferences
  ui: {
    theme: 'default',
    showWelcome: true,
    autoSave: true,
    historyLimit: 100
  },
  
  // Chat settings
  chat: {
    systemPrompt: 'You are a helpful AI assistant.',
    saveHistory: true,
    streamResponse: true,
    showTokenCount: true
  }
};

export const configSchema = {
  activeProvider: {
    type: ['string', 'null'],
    enum: ['openai', 'anthropic', 'deepseek', null]
  },
  providers: {
    type: 'object',
    properties: {
      openai: { type: 'object' },
      anthropic: { type: 'object' },
      deepseek: { type: 'object' }
    }
  },
  ui: {
    type: 'object',
    properties: {
      theme: { type: 'string' },
      showWelcome: { type: 'boolean' },
      autoSave: { type: 'boolean' },
      historyLimit: { type: 'number', minimum: 1, maximum: 1000 }
    }
  },
  chat: {
    type: 'object',
    properties: {
      systemPrompt: { type: 'string' },
      saveHistory: { type: 'boolean' },
      streamResponse: { type: 'boolean' },
      showTokenCount: { type: 'boolean' }
    }
  }
};
