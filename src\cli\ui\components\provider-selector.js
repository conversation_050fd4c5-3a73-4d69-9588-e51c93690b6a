import inquirer from 'inquirer';
import chalk from 'chalk';
import configManager from '../../../config/config-manager.js';
import ProviderFactory from '../../../providers/index.js';

export class ProviderSelector {
  /**
   * Show provider selection menu
   */
  static async selectProvider() {
    const availableProviders = configManager.getAvailableProviders();
    const configuredProviders = configManager.getConfiguredProviders();
    const currentProvider = configManager.get('activeProvider');

    // Create choices with status indicators
    const choices = availableProviders.map(provider => {
      const isConfigured = configuredProviders.includes(provider);
      const isCurrent = provider === currentProvider;
      
      let name = provider.charAt(0).toUpperCase() + provider.slice(1);
      let status = '';
      
      if (isCurrent) {
        status = chalk.green(' (current)');
      } else if (isConfigured) {
        status = chalk.blue(' (configured)');
      } else {
        status = chalk.gray(' (not configured)');
      }
      
      return {
        name: `${name}${status}`,
        value: provider,
        short: name
      };
    });

    // Add separator and management options
    choices.push(
      new inquirer.Separator(),
      {
        name: chalk.yellow('Configure new provider'),
        value: 'configure',
        short: 'Configure'
      },
      {
        name: chalk.cyan('Test all providers'),
        value: 'test',
        short: 'Test'
      },
      {
        name: chalk.gray('Back to main menu'),
        value: 'back',
        short: 'Back'
      }
    );

    const { selectedProvider } = await inquirer.prompt([
      {
        type: 'list',
        name: 'selectedProvider',
        message: 'Select a provider:',
        choices,
        pageSize: 10
      }
    ]);

    return selectedProvider;
  }

  /**
   * Show provider configuration status
   */
  static async showProviderStatus() {
    const providers = ProviderFactory.getAllProviderInfo();

    console.log(chalk.bold('\nProvider Status:\n'));

    for (const [name, info] of Object.entries(providers)) {
      const displayName = name.charAt(0).toUpperCase() + name.slice(1);
      const status = info.configured ?
        chalk.green('CONFIGURED') :
        chalk.red('NOT CONFIGURED');

      console.log(`${chalk.bold(displayName)}: ${status}`);

      if (info.configured) {
        console.log(`  Default model: ${chalk.cyan(info.defaultModel)}`);
        console.log(`  Available models: ${info.models.length}`);
      }

      if (info.error) {
        console.log(`  ${chalk.red('Error:')} ${info.error}`);
      }

      console.log();
    }
  }

  /**
   * Test provider connections
   */
  static async testProviders() {
    const configuredProviders = configManager.getConfiguredProviders();

    if (configuredProviders.length === 0) {
      console.log(chalk.yellow('No providers are configured yet.'));
      return;
    }

    console.log(chalk.bold('\nTesting provider connections...\n'));

    const results = await ProviderFactory.testAllProviders();

    for (const [name, result] of Object.entries(results)) {
      const displayName = name.charAt(0).toUpperCase() + name.slice(1);
      const status = result.success ?
        chalk.green('CONNECTED') :
        chalk.red('FAILED');

      console.log(`${chalk.bold(displayName)}: ${status}`);

      if (!result.success) {
        console.log(`  ${chalk.red('Error:')} ${result.message}`);
      }

      console.log();
    }
  }

  /**
   * Set active provider
   */
  static async setActiveProvider() {
    const configuredProviders = configManager.getConfiguredProviders();
    
    if (configuredProviders.length === 0) {
      console.log(chalk.yellow('No providers are configured yet. Please configure a provider first.'));
      return null;
    }

    const currentProvider = configManager.get('activeProvider');
    
    const choices = configuredProviders.map(provider => {
      const name = provider.charAt(0).toUpperCase() + provider.slice(1);
      const isCurrent = provider === currentProvider;
      
      return {
        name: isCurrent ? `${name} ${chalk.green('(current)')}` : name,
        value: provider,
        short: name
      };
    });

    const { provider } = await inquirer.prompt([
      {
        type: 'list',
        name: 'provider',
        message: 'Select active provider:',
        choices,
        default: currentProvider
      }
    ]);

    configManager.setActiveProvider(provider);
    console.log(chalk.green(`Active provider set to ${provider.charAt(0).toUpperCase() + provider.slice(1)}`));
    
    return provider;
  }
}
