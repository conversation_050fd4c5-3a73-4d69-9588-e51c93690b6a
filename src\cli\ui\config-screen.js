import inquirer from 'inquirer';
import chalk from 'chalk';
import boxen from 'boxen';
import figlet from 'figlet';
import { ProviderSelector } from './components/provider-selector.js';
import { ApiKeyInput } from './components/api-key-input.js';
import { ModelSelector } from './components/model-selector.js';
import configManager from '../../config/config-manager.js';
import logger from '../../utils/logger.js';

export class ConfigScreen {
  /**
   * Show the main configuration screen
   */
  static async show() {
    console.clear();
    this.showWelcome();
    
    let running = true;
    
    while (running) {
      try {
        const action = await this.showMainMenu();
        
        switch (action) {
          case 'configure':
            await this.configureProvider();
            break;
          case 'select':
            await this.selectActiveProvider();
            break;
          case 'status':
            await this.showStatus();
            break;
          case 'test':
            await this.testProviders();
            break;
          case 'settings':
            await this.showSettings();
            break;
          case 'chat':
            if (this.canStartChat()) {
              running = false;
              return 'chat';
            }
            break;
          case 'exit':
            running = false;
            return 'exit';
        }
        
        if (running) {
          await this.pressAnyKey();
        }
        
      } catch (error) {
        logger.error('Configuration error:', error.message);
        console.log(chalk.red(`Error: ${error.message}`));
        await this.pressAnyKey();
      }
    }
  }

  /**
   * Show welcome message
   */
  static showWelcome() {
    const title = figlet.textSync('LLM CLI', {
      font: 'Small',
      horizontalLayout: 'default',
      verticalLayout: 'default'
    });

    const welcomeBox = boxen(
      chalk.cyan(title) + '\n\n' +
      chalk.white('A modern CLI for interacting with multiple LLM providers\n') +
      chalk.gray('Supports OpenAI, Anthropic, and DeepSeek'),
      {
        padding: 1,
        margin: 1,
        borderStyle: 'round',
        borderColor: 'cyan',
        textAlignment: 'center'
      }
    );

    console.log(welcomeBox);
  }

  /**
   * Show main configuration menu
   */
  static async showMainMenu() {
    const configuredProviders = configManager.getConfiguredProviders();
    
    // Status indicators
    const statusLine = this.getStatusLine();
    console.log(statusLine);
    console.log();

    const choices = [
      {
        name: 'Configure Provider',
        value: 'configure',
        short: 'Configure'
      },
      {
        name: 'Select Active Provider',
        value: 'select',
        short: 'Select',
        disabled: configuredProviders.length === 0 ? 'No providers configured' : false
      },
      {
        name: 'Show Status',
        value: 'status',
        short: 'Status'
      },
      {
        name: 'Test Connections',
        value: 'test',
        short: 'Test',
        disabled: configuredProviders.length === 0 ? 'No providers configured' : false
      },
      {
        name: 'Settings',
        value: 'settings',
        short: 'Settings'
      },
      new inquirer.Separator(),
      {
        name: chalk.green('Start Chat'),
        value: 'chat',
        short: 'Chat',
        disabled: !this.canStartChat() ? 'Configure a provider first' : false
      },
      {
        name: chalk.gray('Exit'),
        value: 'exit',
        short: 'Exit'
      }
    ];

    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: 'What would you like to do?',
        choices,
        pageSize: 10
      }
    ]);

    return action;
  }

  /**
   * Get status line for display
   */
  static getStatusLine() {
    const activeProvider = configManager.get('activeProvider');
    const configuredProviders = configManager.getConfiguredProviders();
    
    let status = '';
    
    if (activeProvider) {
      status += chalk.green(`Active: ${activeProvider.charAt(0).toUpperCase() + activeProvider.slice(1)}`);
    } else {
      status += chalk.yellow('No active provider');
    }
    
    status += chalk.gray(' | ');
    status += chalk.blue(`Configured: ${configuredProviders.length}/3`);
    
    return boxen(status, {
      padding: { top: 0, bottom: 0, left: 1, right: 1 },
      borderStyle: 'single',
      borderColor: 'gray'
    });
  }

  /**
   * Configure a provider
   */
  static async configureProvider() {
    console.clear();
    console.log(chalk.bold('Provider Configuration\n'));
    
    const provider = await ProviderSelector.selectProvider();
    
    if (!provider || provider === 'back') {
      return;
    }
    
    if (provider === 'configure') {
      // Show provider selection for configuration
      const availableProviders = configManager.getAvailableProviders();
      
      const { selectedProvider } = await inquirer.prompt([
        {
          type: 'list',
          name: 'selectedProvider',
          message: 'Which provider would you like to configure?',
          choices: availableProviders.map(p => ({
            name: p.charAt(0).toUpperCase() + p.slice(1),
            value: p
          }))
        }
      ]);
      
      await this.configureSpecificProvider(selectedProvider);
    } else if (provider === 'test') {
      await ProviderSelector.testProviders();
    } else {
      await this.configureSpecificProvider(provider);
    }
  }

  /**
   * Configure a specific provider
   */
  static async configureSpecificProvider(providerName) {
    console.clear();
    
    // Configure API key
    await ApiKeyInput.configureApiKey(providerName);
    
    // Configure model settings
    const { configureModel } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'configureModel',
        message: 'Configure model settings?',
        default: false
      }
    ]);
    
    if (configureModel) {
      await ModelSelector.selectModel(providerName);
    }
    
    // Set as active provider if none is set
    const activeProvider = configManager.get('activeProvider');
    if (!activeProvider && configManager.isProviderConfigured(providerName)) {
      const { setActive } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'setActive',
          message: `Set ${providerName} as active provider?`,
          default: true
        }
      ]);
      
      if (setActive) {
        configManager.setActiveProvider(providerName);
        console.log(chalk.green(`${providerName} set as active provider`));
      }
    }
  }

  /**
   * Select active provider
   */
  static async selectActiveProvider() {
    console.clear();
    await ProviderSelector.setActiveProvider();
  }

  /**
   * Show provider status
   */
  static async showStatus() {
    console.clear();
    await ProviderSelector.showProviderStatus();
  }

  /**
   * Test provider connections
   */
  static async testProviders() {
    console.clear();
    await ProviderSelector.testProviders();
  }

  /**
   * Show settings menu
   */
  static async showSettings() {
    console.clear();
    console.log(chalk.bold('Settings\n'));
    
    const config = configManager.getConfig();
    
    console.log('Current configuration:');
    console.log(`  Theme: ${chalk.cyan(config.ui.theme)}`);
    console.log(`  Show welcome: ${chalk.cyan(config.ui.showWelcome)}`);
    console.log(`  Auto save: ${chalk.cyan(config.ui.autoSave)}`);
    console.log(`  History limit: ${chalk.cyan(config.ui.historyLimit)}`);
    console.log(`  System prompt: ${chalk.cyan(config.chat.systemPrompt.substring(0, 50))}...`);
    console.log();
    
    // Add settings modification options here if needed
  }

  /**
   * Check if chat can be started
   */
  static canStartChat() {
    const activeProvider = configManager.get('activeProvider');
    return activeProvider && configManager.isProviderConfigured(activeProvider);
  }

  /**
   * Wait for user input
   */
  static async pressAnyKey() {
    await inquirer.prompt([
      {
        type: 'input',
        name: 'continue',
        message: 'Press Enter to continue...'
      }
    ]);
    console.clear();
  }
}
