import { ConfigScreen } from '../ui/config-screen.js';
import { ProviderSelector } from '../ui/components/provider-selector.js';
import { ApiKeyInput } from '../ui/components/api-key-input.js';
import { ModelSelector } from '../ui/components/model-selector.js';
import configManager from '../../config/config-manager.js';
import ProviderFactory from '../../providers/index.js';
import logger from '../../utils/logger.js';
import chalk from 'chalk';

/**
 * Configuration command
 */
export async function configCommand(options = {}) {
  try {
    if (options.interactive) {
      // Show full configuration screen
      await ConfigScreen.show();
    } else if (options.provider) {
      // Configure specific provider
      await configureProvider(options.provider, options);
    } else if (options.list) {
      // List providers
      await listProviders();
    } else if (options.test) {
      // Test providers
      await testProviders();
    } else if (options.status) {
      // Show status
      await showStatus();
    } else {
      // Default: show configuration screen
      await ConfigScreen.show();
    }
  } catch (error) {
    logger.error('Config command error:', error.message);
    console.log(chalk.red(`Error: ${error.message}`));
    process.exit(1);
  }
}

/**
 * Configure a specific provider
 */
async function configureProvider(providerName, options) {
  if (!configManager.isValidProvider(providerName)) {
    console.log(chalk.red(`Invalid provider: ${providerName}`));
    console.log(chalk.gray(`Available providers: ${configManager.getAvailableProviders().join(', ')}`));
    return;
  }

  console.log(chalk.bold(`Configuring ${providerName}...\n`));

  // Configure API key
  if (options.apiKey) {
    configManager.updateProvider(providerName, { apiKey: options.apiKey });
    console.log(chalk.green('API key updated'));
  } else {
    await ApiKeyInput.configureApiKey(providerName);
  }

  // Configure model
  if (options.model) {
    const providerConfig = configManager.get(`providers.${providerName}`);
    if (providerConfig.models.includes(options.model)) {
      configManager.updateProvider(providerName, { defaultModel: options.model });
      console.log(chalk.green(`Default model set to ${options.model}`));
    } else {
      console.log(chalk.red(`Invalid model: ${options.model}`));
      console.log(chalk.gray(`Available models: ${providerConfig.models.join(', ')}`));
    }
  }

  // Set as active
  if (options.setActive && configManager.isProviderConfigured(providerName)) {
    configManager.setActiveProvider(providerName);
    console.log(chalk.green(`${providerName} set as active provider`));
  }
}

/**
 * List all providers
 */
async function listProviders() {
  console.log(chalk.bold('Available Providers:\n'));
  
  const providers = ProviderFactory.getAllProviderInfo();
  const activeProvider = configManager.get('activeProvider');
  
  for (const [name, info] of Object.entries(providers)) {
    const displayName = name.charAt(0).toUpperCase() + name.slice(1);
    const isActive = name === activeProvider;
    const status = info.configured ?
      chalk.green('CONFIGURED') :
      chalk.red('NOT CONFIGURED');
    
    let line = `${chalk.bold(displayName)}: ${status}`;
    
    if (isActive) {
      line += chalk.cyan(' (active)');
    }
    
    console.log(line);
    
    if (info.configured) {
      console.log(`  Default model: ${chalk.cyan(info.defaultModel)}`);
      console.log(`  Available models: ${info.models.length}`);
    }
    
    console.log();
  }
}

/**
 * Test provider connections
 */
async function testProviders() {
  const configuredProviders = configManager.getConfiguredProviders();
  
  if (configuredProviders.length === 0) {
    console.log(chalk.yellow('No providers are configured yet.'));
    return;
  }

  console.log(chalk.bold('Testing provider connections...\n'));
  
  const results = await ProviderFactory.testAllProviders();
  
  for (const [name, result] of Object.entries(results)) {
    const displayName = name.charAt(0).toUpperCase() + name.slice(1);
    const status = result.success ?
      chalk.green('CONNECTED') :
      chalk.red('FAILED');
    
    console.log(`${chalk.bold(displayName)}: ${status}`);
    
    if (!result.success) {
      console.log(`  ${chalk.red('Error:')} ${result.message}`);
    }
    
    console.log();
  }
}

/**
 * Show configuration status
 */
async function showStatus() {
  const activeProvider = configManager.get('activeProvider');
  const configuredProviders = configManager.getConfiguredProviders();
  const config = configManager.getConfig();
  
  console.log(chalk.bold('Configuration Status:\n'));
  
  console.log(`Active provider: ${activeProvider ? 
    chalk.green(activeProvider.charAt(0).toUpperCase() + activeProvider.slice(1)) : 
    chalk.yellow('None')}`);
  
  console.log(`Configured providers: ${chalk.blue(configuredProviders.length)}/3`);
  
  if (configuredProviders.length > 0) {
    console.log(`  ${configuredProviders.map(p => p.charAt(0).toUpperCase() + p.slice(1)).join(', ')}`);
  }
  
  console.log();
  console.log('Settings:');
  console.log(`  System prompt: ${chalk.cyan(config.chat.systemPrompt.substring(0, 50))}...`);
  console.log(`  Show token count: ${chalk.cyan(config.chat.showTokenCount)}`);
  console.log(`  Auto save: ${chalk.cyan(config.ui.autoSave)}`);
  console.log();
}
