import inquirer from 'inquirer';
import chalk from 'chalk';
import { validate<PERSON><PERSON><PERSON><PERSON> } from '../../../utils/validation.js';
import configManager from '../../../config/config-manager.js';

export class ApiKeyInput {
  /**
   * Prompt for API key input with validation
   */
  static async promptForApiKey(providerName) {
    const providerDisplayName = providerName.charAt(0).toUpperCase() + providerName.slice(1);
    const currentKey = configManager.get(`providers.${providerName}.apiKey`);
    
    console.log(chalk.bold(`\n${providerDisplayName} API Key Configuration\n`));
    
    // Show current status
    if (currentKey) {
      const maskedKey = this.maskApiKey(currentKey);
      console.log(`Current API key: ${chalk.cyan(maskedKey)}`);
    } else {
      console.log(chalk.yellow('No API key configured'));
    }
    
    // Show instructions for getting API key
    this.showApiKeyInstructions(providerName);
    
    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: 'What would you like to do?',
        choices: [
          { name: 'Enter new API key', value: 'enter' },
          ...(currentKey ? [{ name: 'Keep current API key', value: 'keep' }] : []),
          { name: 'Remove API key', value: 'remove' },
          { name: 'Back', value: 'back' }
        ]
      }
    ]);

    switch (action) {
      case 'enter':
        return await this.enterApiKey(providerName);
      case 'keep':
        return currentKey;
      case 'remove':
        configManager.updateProvider(providerName, { apiKey: null });
        console.log(chalk.yellow('API key removed'));
        return null;
      case 'back':
        return null;
    }
  }

  /**
   * Enter and validate API key
   */
  static async enterApiKey(providerName) {
    const { apiKey } = await inquirer.prompt([
      {
        type: 'password',
        name: 'apiKey',
        message: 'Enter your API key:',
        mask: '*',
        validate: (input) => {
          if (!input || input.trim() === '') {
            return 'API key cannot be empty';
          }
          
          const validation = validateApiKey(input, providerName);
          if (!validation.isValid) {
            return validation.error;
          }
          
          return true;
        }
      }
    ]);

    // Confirm the API key
    const { confirm } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: `Save this API key for ${providerName}?`,
        default: true
      }
    ]);

    if (confirm) {
      configManager.updateProvider(providerName, { apiKey: apiKey.trim() });
      console.log(chalk.green('API key saved successfully'));
      return apiKey.trim();
    } else {
      console.log(chalk.yellow('API key not saved'));
      return null;
    }
  }

  /**
   * Mask API key for display
   */
  static maskApiKey(apiKey) {
    if (!apiKey || apiKey.length < 8) {
      return '***';
    }
    
    const start = apiKey.substring(0, 4);
    const end = apiKey.substring(apiKey.length - 4);
    const middle = '*'.repeat(Math.max(4, apiKey.length - 8));
    
    return `${start}${middle}${end}`;
  }

  /**
   * Show instructions for obtaining API keys
   */
  static showApiKeyInstructions(providerName) {
    const instructions = {
      openai: {
        url: 'https://platform.openai.com/api-keys',
        steps: [
          '1. Go to https://platform.openai.com/api-keys',
          '2. Sign in to your OpenAI account',
          '3. Click "Create new secret key"',
          '4. Copy the generated key (starts with "sk-")'
        ]
      },
      anthropic: {
        url: 'https://console.anthropic.com/settings/keys',
        steps: [
          '1. Go to https://console.anthropic.com/settings/keys',
          '2. Sign in to your Anthropic account',
          '3. Click "Create Key"',
          '4. Copy the generated key (starts with "sk-ant-")'
        ]
      },
      deepseek: {
        url: 'https://platform.deepseek.com/api_keys',
        steps: [
          '1. Go to https://platform.deepseek.com/api_keys',
          '2. Sign in to your DeepSeek account',
          '3. Click "Create API Key"',
          '4. Copy the generated key (starts with "sk-")'
        ]
      }
    };

    const instruction = instructions[providerName];
    if (instruction) {
      console.log(chalk.gray('\nTo get your API key:'));
      instruction.steps.forEach(step => {
        console.log(chalk.gray(step));
      });
      console.log(chalk.gray(`\nDirect link: ${instruction.url}\n`));
    }
  }

  /**
   * Test API key by making a simple request
   */
  static async testApiKey(providerName) {
    try {
      const ProviderFactory = (await import('../../../providers/index.js')).default;
      const provider = ProviderFactory.createProvider(providerName);
      
      console.log(chalk.blue('Testing API key...'));
      const result = await provider.testConnection();
      
      if (result.success) {
        console.log(chalk.green('API key is valid and working'));
        return true;
      } else {
        console.log(chalk.red(`API key test failed: ${result.message}`));
        return false;
      }
    } catch (error) {
      console.log(chalk.red(`API key test failed: ${error.message}`));
      return false;
    }
  }

  /**
   * Configure API key with testing
   */
  static async configureApiKey(providerName) {
    const apiKey = await this.promptForApiKey(providerName);
    
    if (apiKey) {
      const { shouldTest } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'shouldTest',
          message: 'Test the API key now?',
          default: true
        }
      ]);

      if (shouldTest) {
        await this.testApiKey(providerName);
      }
    }
    
    return apiKey;
  }
}
