import chalk from 'chalk';

class Logger {
  constructor() {
    this.levels = {
      ERROR: 0,
      WARN: 1,
      INFO: 2,
      DEBUG: 3
    };
    
    this.currentLevel = this.levels.INFO;
  }

  setLevel(level) {
    if (typeof level === 'string') {
      this.currentLevel = this.levels[level.toUpperCase()] || this.levels.INFO;
    } else {
      this.currentLevel = level;
    }
  }

  error(message, ...args) {
    if (this.currentLevel >= this.levels.ERROR) {
      console.error(chalk.red('ERROR:'), message, ...args);
    }
  }

  warn(message, ...args) {
    if (this.currentLevel >= this.levels.WARN) {
      console.warn(chalk.yellow('WARN:'), message, ...args);
    }
  }

  info(message, ...args) {
    if (this.currentLevel >= this.levels.INFO) {
      console.log(chalk.blue('INFO:'), message, ...args);
    }
  }

  success(message, ...args) {
    if (this.currentLevel >= this.levels.INFO) {
      console.log(chalk.green('SUCCESS:'), message, ...args);
    }
  }

  debug(message, ...args) {
    if (this.currentLevel >= this.levels.DEBUG) {
      console.log(chalk.gray('DEBUG:'), message, ...args);
    }
  }

  // Utility methods for common scenarios
  apiRequest(method, url) {
    this.debug(`${method.toUpperCase()} ${url}`);
  }

  apiResponse(status, data) {
    if (status >= 200 && status < 300) {
      this.debug(`Response: ${status} - ${JSON.stringify(data).substring(0, 100)}...`);
    } else {
      this.error(`API Error: ${status} - ${JSON.stringify(data)}`);
    }
  }

  configUpdate(key, value) {
    this.debug(`Config updated: ${key} = ${value}`);
  }

  userAction(action) {
    this.debug(`User action: ${action}`);
  }
}

export default new Logger();
