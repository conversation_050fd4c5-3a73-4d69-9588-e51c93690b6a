import { ChatInterface } from '../ui/chat-interface.js';
import configManager from '../../config/config-manager.js';
import ProviderFactory from '../../providers/index.js';
import logger from '../../utils/logger.js';
import chalk from 'chalk';

/**
 * Chat command
 */
export async function chatCommand(message, options = {}) {
  try {
    // Check if provider is configured
    const activeProvider = configManager.get('activeProvider');
    
    if (!activeProvider || !configManager.isProviderConfigured(activeProvider)) {
      console.log(chalk.red('No provider configured. Please run configuration first:'));
      console.log(chalk.gray('  llm-cli config'));
      process.exit(1);
    }

    if (message) {
      // Single message mode
      await sendSingleMessage(message, options);
    } else {
      // Interactive chat mode
      const chat = new ChatInterface();
      await chat.start();
    }
    
  } catch (error) {
    logger.error('Chat command error:', error.message);
    console.log(chalk.red(`Error: ${error.message}`));
    process.exit(1);
  }
}

/**
 * Send a single message and get response
 */
async function sendSingleMessage(message, options) {
  try {
    const activeProvider = configManager.getActiveProvider();
    const provider = ProviderFactory.createProvider(activeProvider.name);
    
    const model = options.model || activeProvider.config.defaultModel;
    const systemPrompt = options.system || configManager.get('chat.systemPrompt');
    
    console.log(chalk.blue('Sending message...'));
    
    const response = await provider.sendMessage(
      [{ role: 'user', content: message }],
      {
        model,
        systemPrompt,
        maxTokens: options.maxTokens,
        temperature: options.temperature
      }
    );
    
    // Display response
    console.log();
    console.log(chalk.green('Response:'));
    console.log(response.content);
    
    // Show token usage if requested
    if (options.tokens && response.usage) {
      console.log();
      console.log(chalk.gray(`Tokens used: ${response.usage.totalTokens} (${response.usage.promptTokens} + ${response.usage.completionTokens})`));
    }
    
  } catch (error) {
    console.log(chalk.red(`Error: ${error.message}`));
    process.exit(1);
  }
}

/**
 * List available models for active provider
 */
export async function listModels() {
  try {
    const activeProvider = configManager.getActiveProvider();
    
    if (!activeProvider) {
      console.log(chalk.red('No active provider configured'));
      return;
    }
    
    const config = activeProvider.config;
    const providerName = activeProvider.name.charAt(0).toUpperCase() + activeProvider.name.slice(1);
    
    console.log(chalk.bold(`${providerName} Models:\n`));
    
    config.models.forEach(model => {
      const isDefault = model === config.defaultModel;
      const marker = isDefault ? chalk.green('→') : ' ';
      const suffix = isDefault ? chalk.green(' (default)') : '';
      
      console.log(`${marker} ${model}${suffix}`);
    });
    
    console.log();
    
  } catch (error) {
    console.log(chalk.red(`Error: ${error.message}`));
  }
}
