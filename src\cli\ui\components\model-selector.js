import inquirer from 'inquirer';
import chalk from 'chalk';
import configManager from '../../../config/config-manager.js';

export class ModelSelector {
  /**
   * Select model for a provider
   */
  static async selectModel(providerName) {
    const providerConfig = configManager.get(`providers.${providerName}`);
    
    if (!providerConfig) {
      throw new Error(`Provider ${providerName} not found`);
    }

    const { models, defaultModel } = providerConfig;
    const providerDisplayName = providerName.charAt(0).toUpperCase() + providerName.slice(1);
    
    console.log(chalk.bold(`\n${providerDisplayName} Model Selection\n`));
    console.log(`Current default model: ${chalk.cyan(defaultModel)}\n`);

    // Create choices with current default indicator
    const choices = models.map(model => ({
      name: model === defaultModel ? `${model} ${chalk.green('(current default)')}` : model,
      value: model,
      short: model
    }));

    // Add management options
    choices.push(
      new inquirer.Separator(),
      {
        name: chalk.yellow('Configure model settings'),
        value: 'configure',
        short: 'Configure'
      },
      {
        name: chalk.gray('Back'),
        value: 'back',
        short: 'Back'
      }
    );

    const { selectedModel } = await inquirer.prompt([
      {
        type: 'list',
        name: 'selectedModel',
        message: 'Select a model:',
        choices,
        pageSize: 15
      }
    ]);

    if (selectedModel === 'configure') {
      return await this.configureModelSettings(providerName);
    } else if (selectedModel === 'back') {
      return null;
    } else {
      // Set as default model
      configManager.updateProvider(providerName, { defaultModel: selectedModel });
      console.log(chalk.green(`Default model set to ${selectedModel}`));
      return selectedModel;
    }
  }

  /**
   * Configure model settings (temperature, max tokens, etc.)
   */
  static async configureModelSettings(providerName) {
    const providerConfig = configManager.get(`providers.${providerName}`);
    const providerDisplayName = providerName.charAt(0).toUpperCase() + providerName.slice(1);
    
    console.log(chalk.bold(`\n${providerDisplayName} Model Settings\n`));
    
    // Show current settings
    console.log('Current settings:');
    console.log(`  Max tokens: ${chalk.cyan(providerConfig.maxTokens)}`);
    console.log(`  Temperature: ${chalk.cyan(providerConfig.temperature)}`);
    console.log();

    const settings = await inquirer.prompt([
      {
        type: 'number',
        name: 'maxTokens',
        message: 'Max tokens (1-32000):',
        default: providerConfig.maxTokens,
        validate: (input) => {
          const num = parseInt(input);
          if (isNaN(num) || num < 1 || num > 32000) {
            return 'Max tokens must be between 1 and 32000';
          }
          return true;
        }
      },
      {
        type: 'number',
        name: 'temperature',
        message: 'Temperature (0.0-2.0):',
        default: providerConfig.temperature,
        validate: (input) => {
          const num = parseFloat(input);
          if (isNaN(num) || num < 0 || num > 2) {
            return 'Temperature must be between 0.0 and 2.0';
          }
          return true;
        }
      }
    ]);

    // Confirm changes
    const { confirm } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: 'Save these settings?',
        default: true
      }
    ]);

    if (confirm) {
      configManager.updateProvider(providerName, {
        maxTokens: parseInt(settings.maxTokens),
        temperature: parseFloat(settings.temperature)
      });
      console.log(chalk.green('Model settings saved'));
    } else {
      console.log(chalk.yellow('Settings not saved'));
    }

    return null;
  }

  /**
   * Show model information
   */
  static showModelInfo(providerName) {
    const providerConfig = configManager.get(`providers.${providerName}`);
    const providerDisplayName = providerName.charAt(0).toUpperCase() + providerName.slice(1);
    
    console.log(chalk.bold(`\n${providerDisplayName} Models\n`));
    
    console.log(`Default model: ${chalk.cyan(providerConfig.defaultModel)}`);
    console.log(`Available models (${providerConfig.models.length}):`);
    
    providerConfig.models.forEach((model) => {
      const isDefault = model === providerConfig.defaultModel;
      const marker = isDefault ? chalk.green('→') : ' ';
      console.log(`${marker} ${model}`);
    });
    
    console.log();
    console.log('Settings:');
    console.log(`  Max tokens: ${chalk.cyan(providerConfig.maxTokens)}`);
    console.log(`  Temperature: ${chalk.cyan(providerConfig.temperature)}`);
    console.log();
  }

  /**
   * Quick model selection for chat
   */
  static async quickSelectModel(providerName) {
    const providerConfig = configManager.get(`providers.${providerName}`);
    const { models, defaultModel } = providerConfig;
    
    // If only one model, return it
    if (models.length === 1) {
      return models[0];
    }

    const { model } = await inquirer.prompt([
      {
        type: 'list',
        name: 'model',
        message: 'Select model for this session:',
        choices: models.map(model => ({
          name: model === defaultModel ? `${model} (default)` : model,
          value: model
        })),
        default: defaultModel
      }
    ]);

    return model;
  }

  /**
   * Get model recommendations based on use case
   */
  static getModelRecommendations(providerName, useCase = 'general') {
    const recommendations = {
      openai: {
        general: 'gpt-4',
        coding: 'gpt-4',
        fast: 'gpt-3.5-turbo',
        creative: 'gpt-4'
      },
      anthropic: {
        general: 'claude-3-5-sonnet-20241022',
        coding: 'claude-3-5-sonnet-20241022',
        fast: 'claude-3-5-haiku-20241022',
        creative: 'claude-3-opus-20240229'
      },
      deepseek: {
        general: 'deepseek-chat',
        coding: 'deepseek-coder',
        fast: 'deepseek-chat',
        creative: 'deepseek-chat'
      }
    };

    return recommendations[providerName]?.[useCase] || null;
  }
}
