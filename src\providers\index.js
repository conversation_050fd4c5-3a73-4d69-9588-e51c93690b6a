import { OpenAIProvider } from './openai.js';
import { AnthropicProvider } from './anthropic.js';
import { DeepSeekProvider } from './deepseek.js';
import configManager from '../config/config-manager.js';
import logger from '../utils/logger.js';

/**
 * Provider factory for creating LLM provider instances
 */
export class ProviderFactory {
  static providers = {
    openai: OpenAIProvider,
    anthropic: AnthropicProvider,
    deepseek: DeepSeekProvider
  };

  /**
   * Create a provider instance
   */
  static createProvider(providerName, config = null) {
    const ProviderClass = this.providers[providerName];
    
    if (!ProviderClass) {
      throw new Error(`Unknown provider: ${providerName}`);
    }

    // Use provided config or get from config manager
    const providerConfig = config || configManager.get(`providers.${providerName}`);
    
    if (!providerConfig) {
      throw new Error(`No configuration found for provider: ${providerName}`);
    }

    logger.debug(`Creating ${providerName} provider instance`);
    return new ProviderClass(providerConfig);
  }

  /**
   * Get the active provider instance
   */
  static getActiveProvider() {
    const activeProviderName = configManager.get('activeProvider');
    
    if (!activeProviderName) {
      throw new Error('No active provider configured');
    }

    return this.createProvider(activeProviderName);
  }

  /**
   * Get all available provider names
   */
  static getAvailableProviders() {
    return Object.keys(this.providers);
  }

  /**
   * Check if a provider is available
   */
  static isProviderAvailable(providerName) {
    return providerName in this.providers;
  }

  /**
   * Test all configured providers
   */
  static async testAllProviders() {
    const results = {};
    const configuredProviders = configManager.getConfiguredProviders();

    for (const providerName of configuredProviders) {
      try {
        const provider = this.createProvider(providerName);
        const result = await provider.testConnection();
        results[providerName] = result;
      } catch (error) {
        results[providerName] = {
          success: false,
          message: error.message
        };
      }
    }

    return results;
  }

  /**
   * Get provider info for all available providers
   */
  static getAllProviderInfo() {
    const info = {};
    
    for (const providerName of this.getAvailableProviders()) {
      try {
        const provider = this.createProvider(providerName);
        info[providerName] = provider.getInfo();
      } catch (error) {
        info[providerName] = {
          name: providerName,
          configured: false,
          error: error.message
        };
      }
    }

    return info;
  }
}

// Export individual providers for direct use
export { OpenAIProvider, AnthropicProvider, DeepSeekProvider };

// Export default instance
export default ProviderFactory;
