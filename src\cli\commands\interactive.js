import { ConfigScreen } from '../ui/config-screen.js';
import { ChatInterface } from '../ui/chat-interface.js';
import configManager from '../../config/config-manager.js';
import logger from '../../utils/logger.js';
import chalk from 'chalk';

/**
 * Interactive mode command
 */
export async function interactiveCommand() {
  try {
    logger.info('Starting interactive mode');
    
    // Always show configuration screen first in interactive mode
    const result = await ConfigScreen.show();
    
    if (result === 'chat') {
      // Start chat interface
      const chat = new ChatInterface();
      await chat.start();
    } else if (result === 'exit') {
      console.log(chalk.yellow('Goodbye!'));
      process.exit(0);
    }
    
  } catch (error) {
    logger.error('Interactive mode error:', error.message);
    console.log(chalk.red(`Error: ${error.message}`));
    process.exit(1);
  }
}
