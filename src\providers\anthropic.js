import { BaseProvider } from './base-provider.js';

export class AnthropicProvider extends BaseProvider {
  constructor(config) {
    super(config);
  }

  /**
   * Get authentication headers for Anthropic
   */
  getAuthHeaders() {
    return {
      'x-api-key': this.apiKey,
      'Content-Type': 'application/json',
      'anthropic-version': '2023-06-01'
    };
  }

  /**
   * Format messages for Anthropic API
   */
  formatMessages(messages) {
    // Anthropic expects alternating user/assistant messages
    // Filter out system messages as they're handled separately
    return messages
      .filter(msg => msg.role !== 'system')
      .map(msg => ({
        role: msg.role === 'assistant' ? 'assistant' : 'user',
        content: msg.content
      }));
  }

  /**
   * Parse Anthropic API response
   */
  parseResponse(response) {
    if (!response.content || response.content.length === 0) {
      throw new Error('No content received from Anthropic');
    }

    const content = response.content[0];
    
    return {
      content: content.text,
      role: 'assistant',
      finishReason: response.stop_reason,
      usage: {
        promptTokens: response.usage?.input_tokens || 0,
        completionTokens: response.usage?.output_tokens || 0,
        totalTokens: (response.usage?.input_tokens || 0) + (response.usage?.output_tokens || 0)
      },
      model: response.model,
      provider: 'anthropic'
    };
  }

  /**
   * Get additional parameters specific to Anthropic
   */
  getAdditionalParams(options) {
    const params = {};
    
    if (options.systemPrompt) {
      params.system = options.systemPrompt;
    }
    
    if (options.topP !== undefined) {
      params.top_p = options.topP;
    }
    
    if (options.topK !== undefined) {
      params.top_k = options.topK;
    }
    
    if (options.stop) {
      params.stop_sequences = Array.isArray(options.stop) ? options.stop : [options.stop];
    }
    
    return params;
  }

  /**
   * Override sendMessage to handle Anthropic's different API structure
   */
  async sendMessage(messages, options = {}) {
    try {
      this.validateConfig();
      
      const client = this.createHttpClient();
      const formattedMessages = this.formatMessages(messages);
      
      const requestData = {
        model: options.model || this.defaultModel,
        max_tokens: options.maxTokens || this.maxTokens,
        temperature: options.temperature || this.temperature,
        messages: formattedMessages,
        ...this.getAdditionalParams(options)
      };

      // Remove stream parameter as Anthropic handles it differently
      delete requestData.stream;

      const response = await client.post('/messages', requestData);
      
      return this.parseResponse(response.data);
      
    } catch (error) {
      // Handle Anthropic-specific errors
      if (error.response?.data?.error) {
        const errorData = error.response.data.error;
        throw new Error(`Anthropic API error: ${errorData.message || errorData.type}`);
      }
      
      throw error;
    }
  }

  /**
   * Test connection with Anthropic-specific endpoint
   */
  async testConnection() {
    try {
      const testMessage = [{ role: 'user', content: 'Hello' }];
      await this.sendMessage(testMessage, { maxTokens: 10 });
      return { success: true, message: `${this.name} connection successful` };
    } catch (error) {
      return { success: false, message: error.message };
    }
  }
}
