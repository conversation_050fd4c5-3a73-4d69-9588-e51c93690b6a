import Conf from 'conf';
import { defaultConfig, configSchema } from './default-config.js';
import { validateConfig } from '../utils/validation.js';

class ConfigManager {
  constructor() {
    this.store = new Conf({
      projectName: 'llm-cli',
      defaults: defaultConfig,
      schema: configSchema
    });
  }

  /**
   * Get the entire configuration
   */
  getConfig() {
    return this.store.store;
  }

  /**
   * Get a specific configuration value
   */
  get(key) {
    return this.store.get(key);
  }

  /**
   * Set a configuration value
   */
  set(key, value) {
    this.store.set(key, value);
  }

  /**
   * Get the active provider configuration
   */
  getActiveProvider() {
    const activeProvider = this.get('activeProvider');
    if (!activeProvider) {
      return null;
    }
    return {
      name: activeProvider,
      config: this.get(`providers.${activeProvider}`)
    };
  }

  /**
   * Set the active provider
   */
  setActiveProvider(providerName) {
    if (!this.isValidProvider(providerName)) {
      throw new Error(`Invalid provider: ${providerName}`);
    }
    this.set('activeProvider', providerName);
  }

  /**
   * Update provider configuration
   */
  updateProvider(providerName, config) {
    if (!this.isValidProvider(providerName)) {
      throw new Error(`Invalid provider: ${providerName}`);
    }
    
    const currentConfig = this.get(`providers.${providerName}`);
    const updatedConfig = { ...currentConfig, ...config };
    
    this.set(`providers.${providerName}`, updatedConfig);
  }

  /**
   * Check if provider is valid
   */
  isValidProvider(providerName) {
    const providers = Object.keys(defaultConfig.providers);
    return providers.includes(providerName);
  }

  /**
   * Get available providers
   */
  getAvailableProviders() {
    return Object.keys(defaultConfig.providers);
  }

  /**
   * Check if provider is configured (has API key)
   */
  isProviderConfigured(providerName) {
    const config = this.get(`providers.${providerName}`);
    return config && config.apiKey && config.apiKey.trim() !== '';
  }

  /**
   * Get configured providers
   */
  getConfiguredProviders() {
    return this.getAvailableProviders().filter(provider => 
      this.isProviderConfigured(provider)
    );
  }

  /**
   * Reset configuration to defaults
   */
  reset() {
    this.store.clear();
  }

  /**
   * Validate current configuration
   */
  validate() {
    return validateConfig(this.getConfig());
  }

  /**
   * Export configuration
   */
  export() {
    return JSON.stringify(this.getConfig(), null, 2);
  }

  /**
   * Import configuration
   */
  import(configJson) {
    try {
      const config = JSON.parse(configJson);
      const validation = validateConfig(config);
      
      if (!validation.isValid) {
        throw new Error(`Invalid configuration: ${validation.errors.join(', ')}`);
      }
      
      // Clear existing config and set new one
      this.store.clear();
      Object.keys(config).forEach(key => {
        this.set(key, config[key]);
      });
      
      return true;
    } catch (error) {
      throw new Error(`Failed to import configuration: ${error.message}`);
    }
  }
}

export default new ConfigManager();
