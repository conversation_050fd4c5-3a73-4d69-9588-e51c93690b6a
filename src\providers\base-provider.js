import axios from 'axios';
import logger from '../utils/logger.js';

export class BaseProvider {
  constructor(config) {
    this.config = config;
    this.name = config.name;
    this.apiKey = config.apiKey;
    this.baseURL = config.baseURL;
    this.models = config.models;
    this.defaultModel = config.defaultModel;
    this.maxTokens = config.maxTokens || 4096;
    this.temperature = config.temperature || 0.7;
  }

  /**
   * Validate provider configuration
   */
  validateConfig() {
    if (!this.apiKey) {
      throw new Error(`API key is required for ${this.name}`);
    }
    
    if (!this.baseURL) {
      throw new Error(`Base URL is required for ${this.name}`);
    }
    
    if (!this.models || this.models.length === 0) {
      throw new Error(`Models list is required for ${this.name}`);
    }
    
    if (!this.defaultModel) {
      throw new Error(`Default model is required for ${this.name}`);
    }
  }

  /**
   * Get available models
   */
  getModels() {
    return this.models;
  }

  /**
   * Get default model
   */
  getDefaultModel() {
    return this.defaultModel;
  }

  /**
   * Create HTTP client with authentication
   */
  createHttpClient() {
    return axios.create({
      baseURL: this.baseURL,
      headers: this.getAuthHeaders(),
      timeout: 30000
    });
  }

  /**
   * Get authentication headers (to be implemented by subclasses)
   */
  getAuthHeaders() {
    throw new Error('getAuthHeaders must be implemented by subclass');
  }

  /**
   * Format messages for API (to be implemented by subclasses)
   */
  formatMessages(messages) {
    throw new Error('formatMessages must be implemented by subclass');
  }

  /**
   * Parse API response (to be implemented by subclasses)
   */
  parseResponse(response) {
    throw new Error('parseResponse must be implemented by subclass');
  }

  /**
   * Send chat completion request
   */
  async sendMessage(messages, options = {}) {
    try {
      this.validateConfig();
      
      const client = this.createHttpClient();
      const formattedMessages = this.formatMessages(messages);
      
      const requestData = {
        model: options.model || this.defaultModel,
        messages: formattedMessages,
        max_tokens: options.maxTokens || this.maxTokens,
        temperature: options.temperature || this.temperature,
        stream: options.stream || false,
        ...this.getAdditionalParams(options)
      };

      logger.apiRequest('POST', `${this.baseURL}/chat/completions`);
      logger.debug('Request data:', JSON.stringify(requestData, null, 2));

      const response = await client.post('/chat/completions', requestData);
      
      logger.apiResponse(response.status, response.data);
      
      return this.parseResponse(response.data);
      
    } catch (error) {
      logger.error(`${this.name} API error:`, error.message);
      
      if (error.response) {
        const { status, data } = error.response;
        logger.error(`HTTP ${status}:`, data);
        
        // Handle common HTTP errors
        switch (status) {
          case 401:
            throw new Error(`Authentication failed. Please check your ${this.name} API key.`);
          case 403:
            throw new Error(`Access forbidden. Your ${this.name} API key may not have the required permissions.`);
          case 429:
            throw new Error(`Rate limit exceeded. Please try again later.`);
          case 500:
            throw new Error(`${this.name} server error. Please try again later.`);
          default:
            throw new Error(`${this.name} API error: ${data.error?.message || data.message || 'Unknown error'}`);
        }
      } else if (error.code === 'ECONNABORTED') {
        throw new Error(`Request timeout. ${this.name} API is not responding.`);
      } else if (error.code === 'ENOTFOUND') {
        throw new Error(`Network error. Cannot reach ${this.name} API.`);
      } else {
        throw new Error(`${this.name} error: ${error.message}`);
      }
    }
  }

  /**
   * Get additional parameters for API request (to be overridden by subclasses)
   */
  getAdditionalParams(options) {
    return {};
  }

  /**
   * Test API connection
   */
  async testConnection() {
    try {
      const testMessage = [{ role: 'user', content: 'Hello' }];
      await this.sendMessage(testMessage, { maxTokens: 10 });
      return { success: true, message: `${this.name} connection successful` };
    } catch (error) {
      return { success: false, message: error.message };
    }
  }

  /**
   * Get provider info
   */
  getInfo() {
    return {
      name: this.name,
      models: this.models,
      defaultModel: this.defaultModel,
      maxTokens: this.maxTokens,
      temperature: this.temperature,
      configured: !!this.apiKey
    };
  }
}
