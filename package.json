{"name": "llm-cli", "version": "1.0.0", "description": "A modern CLI terminal interface for interacting with multiple LLM providers", "main": "src/index.js", "bin": {"llm-cli": "./src/index.js"}, "scripts": {"start": "node src/index.js", "dev": "node src/index.js", "test": "node test-cli.js", "config": "node src/index.js config", "chat": "node src/index.js chat"}, "keywords": ["cli", "llm", "ai", "openai", "anthropic", "deepseek", "terminal", "interactive"], "author": "", "license": "MIT", "dependencies": {"commander": "^11.1.0", "inquirer": "^9.2.12", "chalk": "^5.3.0", "ora": "^7.0.1", "boxen": "^7.1.1", "axios": "^1.6.2", "conf": "^12.0.0", "figlet": "^1.7.0", "cli-table3": "^0.6.3", "word-wrap": "^1.2.5"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0"}, "type": "module"}