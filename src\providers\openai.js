import { BaseProvider } from './base-provider.js';

export class OpenAIProvider extends BaseProvider {
  constructor(config) {
    super(config);
  }

  /**
   * Get authentication headers for OpenAI
   */
  getAuthHeaders() {
    return {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json'
    };
  }

  /**
   * Format messages for OpenAI API
   */
  formatMessages(messages) {
    return messages.map(msg => ({
      role: msg.role,
      content: msg.content
    }));
  }

  /**
   * Parse OpenAI API response
   */
  parseResponse(response) {
    if (!response.choices || response.choices.length === 0) {
      throw new Error('No response choices received from OpenAI');
    }

    const choice = response.choices[0];
    
    return {
      content: choice.message.content,
      role: choice.message.role,
      finishReason: choice.finish_reason,
      usage: {
        promptTokens: response.usage?.prompt_tokens || 0,
        completionTokens: response.usage?.completion_tokens || 0,
        totalTokens: response.usage?.total_tokens || 0
      },
      model: response.model,
      provider: 'openai'
    };
  }

  /**
   * Get additional parameters specific to OpenAI
   */
  getAdditionalParams(options) {
    const params = {};
    
    if (options.systemPrompt) {
      // OpenAI handles system prompts as part of messages
      // This will be handled in formatMessages if needed
    }
    
    if (options.presencePenalty !== undefined) {
      params.presence_penalty = options.presencePenalty;
    }
    
    if (options.frequencyPenalty !== undefined) {
      params.frequency_penalty = options.frequencyPenalty;
    }
    
    if (options.topP !== undefined) {
      params.top_p = options.topP;
    }
    
    if (options.stop) {
      params.stop = options.stop;
    }
    
    return params;
  }

  /**
   * Format messages with system prompt support
   */
  formatMessages(messages, systemPrompt = null) {
    const formattedMessages = [];
    
    // Add system prompt if provided
    if (systemPrompt) {
      formattedMessages.push({
        role: 'system',
        content: systemPrompt
      });
    }
    
    // Add user messages
    messages.forEach(msg => {
      formattedMessages.push({
        role: msg.role,
        content: msg.content
      });
    });
    
    return formattedMessages;
  }

  /**
   * Send message with system prompt support
   */
  async sendMessage(messages, options = {}) {
    // Override formatMessages to include system prompt
    const originalFormatMessages = this.formatMessages;
    this.formatMessages = (msgs) => originalFormatMessages.call(this, msgs, options.systemPrompt);
    
    try {
      return await super.sendMessage(messages, options);
    } finally {
      // Restore original method
      this.formatMessages = originalFormatMessages;
    }
  }
}
