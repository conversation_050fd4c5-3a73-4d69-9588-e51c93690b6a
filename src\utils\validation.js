import { configSchema } from '../config/default-config.js';

/**
 * Validate configuration object against schema
 */
export function validateConfig(config) {
  const errors = [];
  
  try {
    // Basic structure validation
    if (!config || typeof config !== 'object') {
      errors.push('Configuration must be an object');
      return { isValid: false, errors };
    }

    // Validate activeProvider
    if (config.activeProvider !== null && config.activeProvider !== undefined) {
      if (!configSchema.activeProvider.enum.includes(config.activeProvider)) {
        errors.push(`Invalid activeProvider: ${config.activeProvider}`);
      }
    }

    // Validate providers
    if (config.providers) {
      Object.keys(config.providers).forEach(providerName => {
        const provider = config.providers[providerName];
        
        if (!provider || typeof provider !== 'object') {
          errors.push(`Provider ${providerName} must be an object`);
          return;
        }

        // Validate required fields
        if (!provider.name || typeof provider.name !== 'string') {
          errors.push(`Provider ${providerName} must have a valid name`);
        }

        if (!provider.baseURL || typeof provider.baseURL !== 'string') {
          errors.push(`Provider ${providerName} must have a valid baseURL`);
        }

        if (!Array.isArray(provider.models) || provider.models.length === 0) {
          errors.push(`Provider ${providerName} must have a valid models array`);
        }

        if (!provider.defaultModel || typeof provider.defaultModel !== 'string') {
          errors.push(`Provider ${providerName} must have a valid defaultModel`);
        }

        // Validate numeric fields
        if (provider.maxTokens && (typeof provider.maxTokens !== 'number' || provider.maxTokens <= 0)) {
          errors.push(`Provider ${providerName} maxTokens must be a positive number`);
        }

        if (provider.temperature && (typeof provider.temperature !== 'number' || provider.temperature < 0 || provider.temperature > 2)) {
          errors.push(`Provider ${providerName} temperature must be between 0 and 2`);
        }
      });
    }

    // Validate UI settings
    if (config.ui) {
      const ui = config.ui;
      
      if (ui.theme && typeof ui.theme !== 'string') {
        errors.push('UI theme must be a string');
      }

      if (ui.showWelcome !== undefined && typeof ui.showWelcome !== 'boolean') {
        errors.push('UI showWelcome must be a boolean');
      }

      if (ui.autoSave !== undefined && typeof ui.autoSave !== 'boolean') {
        errors.push('UI autoSave must be a boolean');
      }

      if (ui.historyLimit && (typeof ui.historyLimit !== 'number' || ui.historyLimit < 1 || ui.historyLimit > 1000)) {
        errors.push('UI historyLimit must be a number between 1 and 1000');
      }
    }

    // Validate chat settings
    if (config.chat) {
      const chat = config.chat;
      
      if (chat.systemPrompt && typeof chat.systemPrompt !== 'string') {
        errors.push('Chat systemPrompt must be a string');
      }

      if (chat.saveHistory !== undefined && typeof chat.saveHistory !== 'boolean') {
        errors.push('Chat saveHistory must be a boolean');
      }

      if (chat.streamResponse !== undefined && typeof chat.streamResponse !== 'boolean') {
        errors.push('Chat streamResponse must be a boolean');
      }

      if (chat.showTokenCount !== undefined && typeof chat.showTokenCount !== 'boolean') {
        errors.push('Chat showTokenCount must be a boolean');
      }
    }

  } catch (error) {
    errors.push(`Validation error: ${error.message}`);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate API key format
 */
export function validateApiKey(apiKey, provider) {
  if (!apiKey || typeof apiKey !== 'string') {
    return { isValid: false, error: 'API key must be a non-empty string' };
  }

  const trimmedKey = apiKey.trim();
  
  if (trimmedKey.length === 0) {
    return { isValid: false, error: 'API key cannot be empty' };
  }

  // Provider-specific validation
  switch (provider) {
    case 'openai':
      if (!trimmedKey.startsWith('sk-')) {
        return { isValid: false, error: 'OpenAI API key must start with "sk-"' };
      }
      break;
      
    case 'anthropic':
      if (!trimmedKey.startsWith('sk-ant-')) {
        return { isValid: false, error: 'Anthropic API key must start with "sk-ant-"' };
      }
      break;
      
    case 'deepseek':
      if (!trimmedKey.startsWith('sk-')) {
        return { isValid: false, error: 'DeepSeek API key must start with "sk-"' };
      }
      break;
  }

  return { isValid: true };
}

/**
 * Validate URL format
 */
export function validateUrl(url) {
  try {
    new URL(url);
    return { isValid: true };
  } catch (error) {
    return { isValid: false, error: 'Invalid URL format' };
  }
}

/**
 * Sanitize user input
 */
export function sanitizeInput(input) {
  if (typeof input !== 'string') {
    return '';
  }

  return input.trim().replace(/[<>]/g, '');
}

/**
 * Validate message content
 */
export function validateMessage(message) {
  if (!message || typeof message !== 'string') {
    return { isValid: false, error: 'Message must be a non-empty string' };
  }

  const trimmed = message.trim();

  if (trimmed.length === 0) {
    return { isValid: false, error: 'Message cannot be empty' };
  }

  if (trimmed.length > 32000) {
    return { isValid: false, error: 'Message is too long (max 32000 characters)' };
  }

  return { isValid: true };
}

/**
 * Validate temperature value
 */
export function validateTemperature(temperature) {
  const temp = parseFloat(temperature);

  if (isNaN(temp)) {
    return { isValid: false, error: 'Temperature must be a number' };
  }

  if (temp < 0 || temp > 2) {
    return { isValid: false, error: 'Temperature must be between 0.0 and 2.0' };
  }

  return { isValid: true, value: temp };
}

/**
 * Validate max tokens value
 */
export function validateMaxTokens(maxTokens) {
  const tokens = parseInt(maxTokens);

  if (isNaN(tokens)) {
    return { isValid: false, error: 'Max tokens must be a number' };
  }

  if (tokens < 1 || tokens > 32000) {
    return { isValid: false, error: 'Max tokens must be between 1 and 32000' };
  }

  return { isValid: true, value: tokens };
}
